# for循环使用、文件读写操作
with open('city.txt', 'r', encoding = 'utf-8') as file:
	city_list = file.read().split('\n')

with open('project.txt', 'r', encoding = 'utf-8') as file:
	project_list = file.read().split('\n')

with open('money.txt', 'r', encoding = 'utf-8') as file:
	money_list = file.read().split('\n')

result = []
for city in city_list:
	if city == '北京':
		continue
	for project in project_list:
		for money in money_list:
			result.append(city + project + money)

with open('keyword.txt', 'wb') as file:
	file.write('\n'.join(result).encode('utf-8')) 