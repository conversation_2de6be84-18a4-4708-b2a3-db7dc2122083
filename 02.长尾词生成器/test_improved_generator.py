#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版长尾词生成器测试文件
用于验证改进效果和功能测试
"""

import unittest
import json
import os
from improved_keyword_generator import ImprovedKeywordGenerator, GeneratedKeyword


class TestImprovedKeywordGenerator(unittest.TestCase):
    """测试改进版关键词生成器"""
    
    def setUp(self):
        """测试前准备"""
        self.generator = ImprovedKeywordGenerator()
        
    def test_config_creation(self):
        """测试配置文件创建"""
        self.assertTrue(os.path.exists("keyword_config.json"))
        
        with open("keyword_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        self.assertIn("data_sources", config)
        self.assertIn("templates", config)
        
    def test_keyword_generation(self):
        """测试关键词生成"""
        keywords = self.generator.generate_keywords(max_keywords=50, min_score=0.5)
        
        self.assertGreater(len(keywords), 0)
        self.assertLessEqual(len(keywords), 50)
        
        # 检查生成的关键词质量
        for kw in keywords:
            self.assertIsInstance(kw, GeneratedKeyword)
            self.assertGreaterEqual(kw.score, 0.5)
            self.assertGreater(len(kw.keyword), 0)
            
    def test_score_calculation(self):
        """测试评分计算"""
        test_cases = [
            ("上海租房多少钱", {"city": "上海", "action": "租房", "price": "多少钱"}),
            ("北京买房价格", {"city": "北京", "action": "买房", "price": "价格"}),
            ("深圳二手房怎么样", {"city": "深圳", "property": "二手房", "modifier": "怎么样"})
        ]
        
        for keyword, components in test_cases:
            score = self.generator.calculate_keyword_score(keyword, components)
            self.assertGreaterEqual(score, 0.0)
            self.assertLessEqual(score, 5.0)
            
    def test_natural_phrase_detection(self):
        """测试自然短语检测"""
        natural_phrases = ["上海租房多少钱", "北京买房攻略", "深圳二手房价格"]
        unnatural_phrases = ["上上上海租房", "12345678901234567890", ""]
        
        for phrase in natural_phrases:
            self.assertTrue(self.generator.is_natural_phrase(phrase))
            
        for phrase in unnatural_phrases:
            self.assertFalse(self.generator.is_natural_phrase(phrase))
            
    def test_export_functionality(self):
        """测试导出功能"""
        keywords = self.generator.generate_keywords(max_keywords=10)
        
        # 测试简单格式导出
        self.generator.export_keywords("test_simple.txt", "simple")
        self.assertTrue(os.path.exists("test_simple.txt"))
        
        # 测试详细格式导出
        self.generator.export_keywords("test_detailed.txt", "detailed")
        self.assertTrue(os.path.exists("test_detailed.txt"))
        
        # 测试JSON格式导出
        self.generator.export_keywords("test_data.json", "json")
        self.assertTrue(os.path.exists("test_data.json"))
        
        # 清理测试文件
        for filename in ["test_simple.txt", "test_detailed.txt", "test_data.json"]:
            if os.path.exists(filename):
                os.remove(filename)
                
    def test_statistics(self):
        """测试统计功能"""
        keywords = self.generator.generate_keywords(max_keywords=20)
        stats = self.generator.get_statistics()
        
        self.assertIn("total_keywords", stats)
        self.assertIn("average_score", stats)
        self.assertIn("categories", stats)
        self.assertEqual(stats["total_keywords"], len(keywords))
        
    def tearDown(self):
        """测试后清理"""
        # 清理可能生成的文件
        cleanup_files = [
            "improved_keywords.txt",
            "keywords_detailed.txt", 
            "keywords_data.json"
        ]
        
        for filename in cleanup_files:
            if os.path.exists(filename):
                os.remove(filename)


def performance_comparison():
    """性能对比测试"""
    print("🔄 执行性能对比测试...")
    
    # 原版生成器模拟
    def original_generator():
        city_list = ["上海", "广州", "深圳", "杭州"]
        project_list = ["租房", "买房"]
        money_list = ["多少钱", "价格"]
        
        result = []
        for city in city_list:
            for project in project_list:
                for money in money_list:
                    result.append(city + project + money)
        return result
    
    # 改进版生成器
    generator = ImprovedKeywordGenerator()
    
    # 生成对比
    original_keywords = original_generator()
    improved_keywords = generator.generate_keywords(max_keywords=100, min_score=1.0)
    
    print(f"\n📊 对比结果:")
    print(f"原版生成数量: {len(original_keywords)}")
    print(f"改进版生成数量: {len(improved_keywords)}")
    print(f"数量提升: {len(improved_keywords) / len(original_keywords):.1f}x")
    
    print(f"\n原版示例:")
    for i, kw in enumerate(original_keywords[:5], 1):
        print(f"  {i}. {kw}")
    
    print(f"\n改进版示例 (评分最高):")
    for i, kw in enumerate(improved_keywords[:5], 1):
        print(f"  {i}. {kw.keyword} (评分: {kw.score:.2f})")
    
    # 多样性分析
    original_patterns = set()
    for kw in original_keywords:
        pattern = ""
        if any(city in kw for city in ["上海", "广州", "深圳", "杭州"]):
            pattern += "城市+"
        if "租房" in kw or "买房" in kw:
            pattern += "动作+"
        if "多少钱" in kw or "价格" in kw:
            pattern += "价格"
        original_patterns.add(pattern)
    
    improved_categories = set(kw.category for kw in improved_keywords)
    
    print(f"\n🎯 多样性分析:")
    print(f"原版模式数量: {len(original_patterns)}")
    print(f"改进版类别数量: {len(improved_categories)}")
    print(f"改进版类别: {', '.join(improved_categories)}")


def quality_analysis():
    """质量分析"""
    print("\n🔍 执行质量分析...")
    
    generator = ImprovedKeywordGenerator()
    keywords = generator.generate_keywords(max_keywords=200, min_score=0.5)
    
    # 按评分分组
    score_groups = {
        "优秀 (4.0+)": [kw for kw in keywords if kw.score >= 4.0],
        "良好 (3.0-3.9)": [kw for kw in keywords if 3.0 <= kw.score < 4.0],
        "一般 (2.0-2.9)": [kw for kw in keywords if 2.0 <= kw.score < 3.0],
        "较差 (1.0-1.9)": [kw for kw in keywords if 1.0 <= kw.score < 2.0],
        "很差 (<1.0)": [kw for kw in keywords if kw.score < 1.0]
    }
    
    print(f"📈 质量分布:")
    for group_name, group_keywords in score_groups.items():
        percentage = len(group_keywords) / len(keywords) * 100
        print(f"  {group_name}: {len(group_keywords)} 个 ({percentage:.1f}%)")
        
        if group_keywords:
            print(f"    示例: {group_keywords[0].keyword}")
    
    # 长度分析
    lengths = [len(kw.keyword) for kw in keywords]
    avg_length = sum(lengths) / len(lengths)
    
    print(f"\n📏 长度分析:")
    print(f"  平均长度: {avg_length:.1f} 字符")
    print(f"  最短: {min(lengths)} 字符")
    print(f"  最长: {max(lengths)} 字符")
    
    # 预估搜索量分析
    volumes = [kw.search_volume_estimate for kw in keywords]
    total_volume = sum(volumes)
    avg_volume = total_volume / len(volumes)
    
    print(f"\n🔍 搜索量分析:")
    print(f"  总预估搜索量: {total_volume:,}")
    print(f"  平均预估搜索量: {avg_volume:.0f}")
    print(f"  最高预估搜索量: {max(volumes):,}")


if __name__ == "__main__":
    print("🧪 开始测试改进版长尾词生成器...")
    
    # 运行单元测试
    print("\n1️⃣ 运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能对比
    print("\n2️⃣ 运行性能对比...")
    performance_comparison()
    
    # 运行质量分析
    print("\n3️⃣ 运行质量分析...")
    quality_analysis()
    
    print("\n✅ 所有测试完成！")
