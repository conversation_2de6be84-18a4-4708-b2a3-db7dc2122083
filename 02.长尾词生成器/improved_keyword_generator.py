#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版长尾词生成器
功能特点：
1. 多维度数据源
2. 语义相关性分析
3. 智能过滤机制
4. 质量评估系统
5. 可配置的生成策略
"""

import json
import random
import itertools
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass
from pathlib import Path
import re


@dataclass
class KeywordTemplate:
    """关键词模板类"""
    pattern: str  # 模板模式，如 "{location}{action}{property_type}{modifier}"
    weight: float  # 模板权重
    category: str  # 模板分类


@dataclass
class GeneratedKeyword:
    """生成的关键词类"""
    keyword: str
    components: Dict[str, str]  # 组成部分
    score: float  # 质量评分
    category: str  # 分类
    search_volume_estimate: int  # 预估搜索量


class ImprovedKeywordGenerator:
    """改进版关键词生成器"""
    
    def __init__(self, config_file: str = "keyword_config.json"):
        self.config_file = config_file
        self.data_sources = {}
        self.templates = []
        self.generated_keywords = []
        self.load_configuration()
        
    def load_configuration(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.data_sources = config.get('data_sources', {})
                self.templates = [
                    KeywordTemplate(**template) 
                    for template in config.get('templates', [])
                ]
        except FileNotFoundError:
            self.create_default_config()
            self.load_configuration()
    
    def create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "data_sources": {
                "locations": {
                    "cities": ["北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都"],
                    "districts": ["朝阳区", "海淀区", "浦东新区", "天河区", "南山区"],
                    "areas": ["CBD", "市中心", "新区", "老城区", "开发区"]
                },
                "property_types": {
                    "basic": ["租房", "买房", "二手房", "新房"],
                    "detailed": ["一居室", "两居室", "三居室", "别墅", "公寓", "写字楼"],
                    "features": ["精装修", "毛坯房", "学区房", "地铁房", "景观房"]
                },
                "actions": {
                    "search": ["找", "寻找", "搜索", "查找"],
                    "inquiry": ["咨询", "了解", "询问"],
                    "transaction": ["购买", "租赁", "出售", "出租"]
                },
                "modifiers": {
                    "price": ["多少钱", "价格", "房价", "租金", "费用"],
                    "quality": ["怎么样", "好不好", "评价", "口碑"],
                    "urgency": ["急租", "急售", "马上", "立即"],
                    "condition": ["便宜", "优质", "性价比高", "划算"]
                },
                "intents": {
                    "informational": ["攻略", "指南", "注意事项", "流程"],
                    "commercial": ["推荐", "哪家好", "排行榜", "对比"],
                    "local": ["附近", "周边", "本地", "当地"]
                }
            },
            "templates": [
                {
                    "pattern": "{city}{property_type}{price}",
                    "weight": 1.0,
                    "category": "basic_price_inquiry"
                },
                {
                    "pattern": "{district}{action}{property_type}",
                    "weight": 0.8,
                    "category": "location_specific"
                },
                {
                    "pattern": "{city}{feature}{property_type}{modifier}",
                    "weight": 0.9,
                    "category": "feature_based"
                },
                {
                    "pattern": "{area}{urgency}{property_type}",
                    "weight": 0.7,
                    "category": "urgent_need"
                },
                {
                    "pattern": "{city}{property_type}{intent}",
                    "weight": 0.6,
                    "category": "informational"
                }
            ]
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
    
    def expand_synonyms(self, word: str, category: str) -> List[str]:
        """扩展同义词"""
        synonym_map = {
            "租房": ["租赁", "出租", "租住"],
            "买房": ["购房", "置业", "购买"],
            "多少钱": ["价格", "费用", "成本", "花费"],
            "怎么样": ["如何", "好不好", "评价"]
        }
        
        synonyms = [word]
        if word in synonym_map:
            synonyms.extend(synonym_map[word])
        
        return synonyms
    
    def calculate_keyword_score(self, keyword: str, components: Dict[str, str]) -> float:
        """计算关键词质量评分"""
        score = 0.0
        
        # 长度评分 (3-15字符为最佳)
        length = len(keyword)
        if 3 <= length <= 15:
            score += 1.0
        elif length < 3 or length > 20:
            score -= 0.5
        
        # 语义完整性评分
        if any(price_word in keyword for price_word in ["多少钱", "价格", "费用"]):
            score += 0.5
        
        if any(location in keyword for location in self.data_sources.get("locations", {}).get("cities", [])):
            score += 0.3
        
        # 避免重复词汇
        words = list(components.values())
        if len(words) != len(set(words)):
            score -= 0.3
        
        # 语法自然度评分
        if self.is_natural_phrase(keyword):
            score += 0.4
        
        return max(0.0, min(5.0, score))
    
    def is_natural_phrase(self, phrase: str) -> bool:
        """检查短语是否自然"""
        # 简单的语法规则检查
        unnatural_patterns = [
            r'(.)\1{2,}',  # 连续重复字符
            r'[0-9]{5,}',  # 过长数字
        ]
        
        for pattern in unnatural_patterns:
            if re.search(pattern, phrase):
                return False
        
        return True
    
    def estimate_search_volume(self, keyword: str) -> int:
        """预估搜索量（简化版本）"""
        base_volume = 100
        
        # 根据关键词特征调整
        if any(city in keyword for city in ["北京", "上海", "深圳"]):
            base_volume *= 3
        
        if "租房" in keyword:
            base_volume *= 2
        elif "买房" in keyword:
            base_volume *= 1.5
        
        if any(urgent in keyword for urgent in ["急", "马上", "立即"]):
            base_volume *= 0.7
        
        return int(base_volume * random.uniform(0.5, 2.0))
    
    def generate_keywords(self, max_keywords: int = 1000, min_score: float = 1.0) -> List[GeneratedKeyword]:
        """生成关键词"""
        self.generated_keywords = []
        seen_keywords = set()
        
        for template in self.templates:
            # 解析模板中的占位符
            placeholders = re.findall(r'\{(\w+)\}', template.pattern)
            
            # 为每个占位符准备候选词
            candidates = {}
            for placeholder in placeholders:
                candidates[placeholder] = self._get_candidates_for_placeholder(placeholder)
            
            # 生成组合
            if candidates:
                combinations = itertools.product(*candidates.values())
                
                for combination in combinations:
                    if len(self.generated_keywords) >= max_keywords:
                        break
                    
                    # 构建关键词
                    components = dict(zip(placeholders, combination))
                    keyword = template.pattern
                    
                    for placeholder, value in components.items():
                        keyword = keyword.replace(f'{{{placeholder}}}', value)
                    
                    # 避免重复
                    if keyword in seen_keywords:
                        continue
                    
                    seen_keywords.add(keyword)
                    
                    # 计算评分
                    score = self.calculate_keyword_score(keyword, components)
                    
                    if score >= min_score:
                        generated_keyword = GeneratedKeyword(
                            keyword=keyword,
                            components=components,
                            score=score,
                            category=template.category,
                            search_volume_estimate=self.estimate_search_volume(keyword)
                        )
                        
                        self.generated_keywords.append(generated_keyword)
        
        # 按评分排序
        self.generated_keywords.sort(key=lambda x: x.score, reverse=True)
        
        return self.generated_keywords[:max_keywords]
    
    def _get_candidates_for_placeholder(self, placeholder: str) -> List[str]:
        """获取占位符的候选词"""
        mapping = {
            'city': self.data_sources.get('locations', {}).get('cities', []),
            'district': self.data_sources.get('locations', {}).get('districts', []),
            'area': self.data_sources.get('locations', {}).get('areas', []),
            'property_type': (
                self.data_sources.get('property_types', {}).get('basic', []) +
                self.data_sources.get('property_types', {}).get('detailed', [])
            ),
            'feature': self.data_sources.get('property_types', {}).get('features', []),
            'action': (
                self.data_sources.get('actions', {}).get('search', []) +
                self.data_sources.get('actions', {}).get('inquiry', [])
            ),
            'price': self.data_sources.get('modifiers', {}).get('price', []),
            'modifier': (
                self.data_sources.get('modifiers', {}).get('quality', []) +
                self.data_sources.get('modifiers', {}).get('condition', [])
            ),
            'urgency': self.data_sources.get('modifiers', {}).get('urgency', []),
            'intent': (
                self.data_sources.get('intents', {}).get('informational', []) +
                self.data_sources.get('intents', {}).get('commercial', [])
            )
        }
        
        return mapping.get(placeholder, [])
    
    def export_keywords(self, filename: str = "improved_keywords.txt", format_type: str = "simple"):
        """导出关键词"""
        with open(filename, 'w', encoding='utf-8') as f:
            if format_type == "simple":
                for kw in self.generated_keywords:
                    f.write(f"{kw.keyword}\n")
            elif format_type == "detailed":
                for kw in self.generated_keywords:
                    f.write(f"{kw.keyword}\t{kw.score:.2f}\t{kw.search_volume_estimate}\t{kw.category}\n")
            elif format_type == "json":
                keywords_data = [
                    {
                        "keyword": kw.keyword,
                        "score": kw.score,
                        "category": kw.category,
                        "search_volume_estimate": kw.search_volume_estimate,
                        "components": kw.components
                    }
                    for kw in self.generated_keywords
                ]
                json.dump(keywords_data, f, ensure_ascii=False, indent=2)
    
    def get_statistics(self) -> Dict:
        """获取生成统计信息"""
        if not self.generated_keywords:
            return {}
        
        categories = {}
        scores = [kw.score for kw in self.generated_keywords]
        
        for kw in self.generated_keywords:
            categories[kw.category] = categories.get(kw.category, 0) + 1
        
        return {
            "total_keywords": len(self.generated_keywords),
            "average_score": sum(scores) / len(scores),
            "max_score": max(scores),
            "min_score": min(scores),
            "categories": categories,
            "total_estimated_volume": sum(kw.search_volume_estimate for kw in self.generated_keywords)
        }


def main():
    """主函数"""
    generator = ImprovedKeywordGenerator()
    
    print("🚀 改进版长尾词生成器启动...")
    print("📊 正在生成关键词...")
    
    # 生成关键词
    keywords = generator.generate_keywords(max_keywords=500, min_score=1.0)
    
    print(f"✅ 成功生成 {len(keywords)} 个高质量长尾词")
    
    # 显示统计信息
    stats = generator.get_statistics()
    print("\n📈 生成统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 显示前10个最高评分的关键词
    print("\n🏆 评分最高的10个关键词:")
    for i, kw in enumerate(keywords[:10], 1):
        print(f"  {i}. {kw.keyword} (评分: {kw.score:.2f}, 预估搜索量: {kw.search_volume_estimate})")
    
    # 导出结果
    generator.export_keywords("improved_keywords.txt", "simple")
    generator.export_keywords("keywords_detailed.txt", "detailed")
    generator.export_keywords("keywords_data.json", "json")
    
    print("\n💾 结果已导出到:")
    print("  - improved_keywords.txt (简单格式)")
    print("  - keywords_detailed.txt (详细格式)")
    print("  - keywords_data.json (JSON格式)")


if __name__ == "__main__":
    main()
