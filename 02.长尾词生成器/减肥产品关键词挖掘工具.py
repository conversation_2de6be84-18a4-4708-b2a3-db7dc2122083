#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
减肥产品关键词挖掘工具
专门针对减肥行业的关键词挖掘和近义词扩展
"""

import json
import random
from typing import List, Dict, Set
from collections import defaultdict


class WeightLossKeywordMiner:
    """减肥产品关键词挖掘器"""
    
    def __init__(self):
        self.keyword_database = self._build_keyword_database()
        self.discovered_keywords = set()
        
    def _build_keyword_database(self) -> Dict[str, List[str]]:
        """构建减肥行业关键词数据库"""
        return {
            # 核心动作词
            "core_actions": [
                "减肥", "瘦身", "减脂", "减重", "塑形", "塑身", "纤体",
                "燃脂", "刷脂", "甩肉", "掉秤", "瘦下来", "变瘦", "控重"
            ],
            
            # 产品类型
            "product_types": [
                "产品", "药", "茶", "咖啡", "代餐", "奶昔", "胶囊", "片剂",
                "粉末", "果冻", "糖果", "饼干", "神器", "仪器", "设备"
            ],
            
            # 效果描述
            "effects": [
                "有效", "快速", "安全", "健康", "天然", "无副作用", "立竿见影",
                "轻松", "简单", "方便", "科学", "专业", "强效", "温和"
            ],
            
            # 目标人群
            "target_groups": [
                "学生", "上班族", "宝妈", "中年", "女性", "男性", "懒人",
                "忙人", "办公室", "久坐", "产后", "中老年"
            ],
            
            # 身体部位
            "body_parts": [
                "全身", "肚子", "腰部", "腿部", "手臂", "脸部", "臀部",
                "大腿", "小腿", "腹部", "背部", "胸部"
            ],
            
            # 使用场景
            "scenarios": [
                "居家", "办公室", "健身房", "户外", "睡前", "饭前", "饭后",
                "运动前", "运动后", "上班", "在家", "旅行"
            ],
            
            # 时间相关
            "time_related": [
                "快速", "7天", "一周", "一个月", "30天", "三个月", "长期",
                "短期", "立即", "马上", "当天", "隔夜"
            ],
            
            # 用户痛点
            "pain_points": [
                "顽固", "反弹", "平台期", "瓶颈", "难减", "易胖", "水肿",
                "便秘", "代谢慢", "食欲大", "控制不住", "没时间运动"
            ],
            
            # 购买意图
            "purchase_intent": [
                "推荐", "哪个好", "排行榜", "对比", "评价", "效果", "价格",
                "多少钱", "便宜", "性价比", "正品", "官网", "购买"
            ],
            
            # 信息需求
            "info_needs": [
                "方法", "攻略", "经验", "心得", "日记", "过程", "注意事项",
                "副作用", "原理", "成分", "使用方法", "怎么用"
            ]
        }
    
    def generate_synonyms(self, base_word: str) -> List[str]:
        """生成近义词"""
        synonyms = []
        
        # 基于词库的近义词扩展
        for category, words in self.keyword_database.items():
            if base_word in words:
                synonyms.extend([w for w in words if w != base_word])
        
        # 手动定义的特殊近义词
        special_synonyms = {
            "减肥": ["瘦身", "减脂", "塑形", "纤体", "减重"],
            "产品": ["神器", "利器", "宝贝", "好物", "单品"],
            "有效": ["管用", "好用", "见效", "有用", "靠谱"],
            "快速": ["迅速", "立即", "马上", "快速", "急速"],
            "安全": ["无害", "健康", "天然", "无副作用", "放心"]
        }
        
        if base_word in special_synonyms:
            synonyms.extend(special_synonyms[base_word])
        
        return list(set(synonyms))
    
    def generate_long_tail_keywords(self, seed_keywords: List[str]) -> List[str]:
        """生成长尾关键词"""
        long_tail_keywords = []
        
        for seed in seed_keywords:
            # 1. 直接组合
            for category, words in self.keyword_database.items():
                for word in words[:5]:  # 限制数量避免过多
                    if word != seed:
                        long_tail_keywords.extend([
                            f"{seed}{word}",
                            f"{word}{seed}",
                            f"{seed}的{word}",
                            f"{word}的{seed}"
                        ])
            
            # 2. 三词组合
            categories = list(self.keyword_database.keys())
            for i in range(min(3, len(categories))):
                cat1 = categories[i]
                for j in range(i+1, min(i+3, len(categories))):
                    cat2 = categories[j]
                    word1 = random.choice(self.keyword_database[cat1][:3])
                    word2 = random.choice(self.keyword_database[cat2][:3])
                    long_tail_keywords.extend([
                        f"{seed}{word1}{word2}",
                        f"{word1}{seed}{word2}",
                        f"{word1}{word2}{seed}"
                    ])
        
        # 去重并过滤
        unique_keywords = list(set(long_tail_keywords))
        filtered_keywords = [kw for kw in unique_keywords if 2 <= len(kw) <= 15]
        
        return filtered_keywords[:200]  # 返回前200个
    
    def analyze_user_intent(self, keyword: str) -> str:
        """分析用户搜索意图"""
        info_indicators = ["怎么", "如何", "方法", "攻略", "注意事项", "原理", "成分"]
        commercial_indicators = ["推荐", "哪个好", "排行榜", "价格", "多少钱", "购买", "正品"]
        comparison_indicators = ["对比", "比较", "区别", "哪个", "还是"]
        
        if any(indicator in keyword for indicator in info_indicators):
            return "信息型"
        elif any(indicator in keyword for indicator in commercial_indicators):
            return "商业型"
        elif any(indicator in keyword for indicator in comparison_indicators):
            return "对比型"
        else:
            return "混合型"
    
    def estimate_competition_level(self, keyword: str) -> str:
        """预估竞争激烈程度"""
        high_competition_words = ["减肥", "瘦身", "减脂", "产品", "药"]
        medium_competition_words = ["方法", "攻略", "经验", "心得"]
        
        if any(word in keyword for word in high_competition_words):
            if len(keyword) <= 4:
                return "高"
            elif len(keyword) <= 6:
                return "中"
            else:
                return "低"
        elif any(word in keyword for word in medium_competition_words):
            return "中"
        else:
            return "低"
    
    def generate_comprehensive_report(self, seed_keywords: List[str]) -> Dict:
        """生成综合关键词报告"""
        print(f"🔍 开始挖掘关键词，种子词：{seed_keywords}")
        
        # 1. 生成近义词
        all_synonyms = []
        for seed in seed_keywords:
            synonyms = self.generate_synonyms(seed)
            all_synonyms.extend(synonyms)
            print(f"  '{seed}' 的近义词：{synonyms[:5]}...")
        
        # 2. 生成长尾关键词
        long_tail_keywords = self.generate_long_tail_keywords(seed_keywords + all_synonyms)
        print(f"📊 生成长尾关键词：{len(long_tail_keywords)} 个")
        
        # 3. 分析和分类
        keyword_analysis = []
        intent_distribution = defaultdict(int)
        competition_distribution = defaultdict(int)
        
        for keyword in long_tail_keywords[:100]:  # 分析前100个
            intent = self.analyze_user_intent(keyword)
            competition = self.estimate_competition_level(keyword)
            
            keyword_analysis.append({
                "keyword": keyword,
                "length": len(keyword),
                "intent": intent,
                "competition": competition,
                "estimated_volume": random.randint(100, 5000)  # 模拟搜索量
            })
            
            intent_distribution[intent] += 1
            competition_distribution[competition] += 1
        
        # 4. 生成报告
        report = {
            "summary": {
                "seed_keywords": seed_keywords,
                "total_synonyms": len(set(all_synonyms)),
                "total_long_tail": len(long_tail_keywords),
                "analyzed_keywords": len(keyword_analysis)
            },
            "intent_distribution": dict(intent_distribution),
            "competition_distribution": dict(competition_distribution),
            "top_keywords": sorted(keyword_analysis, key=lambda x: x["estimated_volume"], reverse=True)[:20],
            "low_competition_keywords": [kw for kw in keyword_analysis if kw["competition"] == "低"][:10],
            "all_keywords": keyword_analysis
        }
        
        return report
    
    def export_keyword_list(self, keywords: List[str], filename: str = "减肥关键词列表.txt"):
        """导出关键词列表"""
        with open(filename, 'w', encoding='utf-8') as f:
            for keyword in keywords:
                f.write(f"{keyword}\n")
        print(f"💾 关键词列表已导出到：{filename}")


def main():
    """主函数演示"""
    print("🚀 减肥产品关键词挖掘工具启动...")
    
    # 创建挖掘器
    miner = WeightLossKeywordMiner()
    
    # 设置种子关键词
    seed_keywords = ["减肥", "瘦身", "减脂"]
    
    # 生成综合报告
    report = miner.generate_comprehensive_report(seed_keywords)
    
    # 显示报告
    print("\n📊 挖掘报告：")
    print(f"种子关键词：{report['summary']['seed_keywords']}")
    print(f"发现近义词：{report['summary']['total_synonyms']} 个")
    print(f"生成长尾词：{report['summary']['total_long_tail']} 个")
    
    print(f"\n🎯 用户意图分布：")
    for intent, count in report['intent_distribution'].items():
        print(f"  {intent}：{count} 个")
    
    print(f"\n⚔️ 竞争程度分布：")
    for level, count in report['competition_distribution'].items():
        print(f"  {level}竞争：{count} 个")
    
    print(f"\n🏆 高搜索量关键词 TOP 10：")
    for i, kw in enumerate(report['top_keywords'][:10], 1):
        print(f"  {i:2d}. {kw['keyword']:<15} (搜索量: {kw['estimated_volume']:>4}, 竞争: {kw['competition']})")
    
    print(f"\n💎 低竞争关键词推荐：")
    for i, kw in enumerate(report['low_competition_keywords'][:10], 1):
        print(f"  {i:2d}. {kw['keyword']:<15} (意图: {kw['intent']}, 搜索量: {kw['estimated_volume']})")
    
    # 导出结果
    all_keywords = [kw['keyword'] for kw in report['all_keywords']]
    miner.export_keyword_list(all_keywords)
    
    # 保存详细报告
    with open("减肥关键词挖掘报告.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("\n✅ 挖掘完成！详细报告已保存到 '减肥关键词挖掘报告.json'")


if __name__ == "__main__":
    main()
