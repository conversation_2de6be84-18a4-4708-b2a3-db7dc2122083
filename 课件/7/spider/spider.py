import requests,re

# rel = requests.get('https://www.qiushibaike.com/')
# print(rel.text)

# rel = requests.get('https://pic.qiushibaike.com/article/image/18REK0TB00XXFICX.jpg')
# with open('1.jpg','wb') as file:
# 	file.write(rel.content)

# data = {
# 	'custname': '1小曾1',
# 	'custtel': '123456',
# 	'custemail': '<EMAIL>',
# 	'size': 'large',
# 	'topping': 'bacon',
# 	'topping': 'cheese',
# 	'delivery': '11:00',
# 	'comments': '谢谢'
# }
# rel = requests.post('http://httpbin.org/post',data=data)
# print(rel.json())

# headers = {
# 	'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
# 	'Accept-Encoding': 'gzip, deflate, br',
# 	'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
# 	'Cache-Control': 'no-cache',
# 	'Connection': 'keep-alive',
# 	'Cookie': '_xsrf=2|ccc4ae92|60ed9fe2564638b9b4d099aa5d058819|1639135427; BAIDU_SSP_lcr=https://www.baidu.com/link?url=YQfTNOHZWlswDNy8XnQqKDPu3Ia4Jb6Hs9navEXbnFuzoUAe6lFmbJ1781OUJBb5&wd=&eqid=b7272e2d0000809b0000000661b338c1; _qqq_uuid_="2|1:0|10:1639135427|10:_qqq_uuid_|56:ZjI2ZTc3ZjU0ZDlkMzI2Njc2NDk0ODE3OTUwNGFiNGEwMDkwOWFhNA==|c0ba8b1e310cfd2e38c40f9410468f91948b3ea3e0fec9b365dd70383a1ad979"; Hm_lvt_2670efbdd59c7e3ed3749b458cafaa37=1639135428; gr_user_id=dafcd588-d1ea-4357-8fa8-4dcc9ce9ac22; ff2672c245bd193c6261e9ab2cd35865_gr_session_id=07cd59e1-e4d5-4022-a3b8-a573ab958c4d; ff2672c245bd193c6261e9ab2cd35865_gr_session_id_07cd59e1-e4d5-4022-a3b8-a573ab958c4d=true; _ga=GA1.2.1561347849.1639135428; _gid=GA1.2.1759073925.1639135428; __cur_art_index=4900; Hm_lpvt_2670efbdd59c7e3ed3749b458cafaa37=1639143095',
# 	'Host': 'www.qiushibaike.com',
# 	'Pragma': 'no-cache',
# 	'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
# 	'sec-ch-ua-mobile': '?0',
# 	'sec-ch-ua-platform': '"Windows"',
# 	'Sec-Fetch-Dest': 'document',
# 	'Sec-Fetch-Mode': 'navigate',
# 	'Sec-Fetch-Site': 'none',
# 	'Sec-Fetch-User': '?1',
# 	'Upgrade-Insecure-Requests': '1',
# 	'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36'
# }

# for i in range(1,6):
# 	print('当前抓取的是：第%s页' % i)
# 	rel = requests.get('https://www.qiushibaike.com/8hr/page/%s/' % i,headers=headers)
# 	re_str = '<a class="recmd-content" href="/article/\d+" target="_blank" onclick="_hmt.push\(\[\'_trackEvent\',\'web-list-user\',\'chick\'\]\)">((?!<img).*?)</a>'
# 	for title in re.findall(re_str,rel.text):
# 		print(re.sub('<img.*?>','',title))
# 	print()

from splinter.browser import Browser

browser = Browser(driver_name='chrome') # 初始化浏览器
browser.visit('https://www.baidu.com/') # 访问指定页面

browser.find_by_id('kw')[0].fill('test')
browser.find_by_id('su')[0].click()