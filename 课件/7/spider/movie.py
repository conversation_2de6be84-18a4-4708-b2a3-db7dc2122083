from splinter.browser import Browser
import re,json,time,random

browser = Browser(driver_name='chrome')

result = []
for i in range(0,51):
	api_url = 'https://sp1.baidu.com/8aQDcjqpAAV3otqbppnN2DJv/api.php?resource_id=28286&from_mid=1&&format=json&ie=utf-8&oe=utf-8&query=%E7%94%B5%E5%BD%B1%E6%8E%92%E8%A1%8C%E6%A6%9C&sort_key=18&sort_type=1&stat0=%E5%96%9C%E5%89%A7&stat1=&stat2=&stat3=&pn=' +str(i * 8)+ '&rn=8&cb=jQuery110208192055336163881_1639231797293&_=1639231797297'
	try:
		browser.visit(api_url)
	except Exception as e:
		print(str(e))
		continue
	re_str = 'jQuery110208192055336163881_1639231797293\((.*?)\)'
	try:
		rel_obj = json.loads(re.findall(re_str,browser.html,re.S)[0])
		data_list = rel_obj['data'][0]['result']
	except Exception as e:
		print(str(e))
		continue
	for data in data_list:
		result.append('%s\t%s' % (data['ename'],data['additional'].replace('豆瓣:','')))
	time.sleep(random.randint(1,3))

with open('movie.txt','wb') as file:
	file.write('\n'.join(result).encode('utf-8'))

browser.quit()