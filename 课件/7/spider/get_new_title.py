from splinter.browser import Browser
from lxml import etree
import re

# browser = Browser(driver_name='chrome')

# for i in range(1,2):
# 	browser.visit('http://ent.qianlong.com/%s.shtml' % i)
# 	# re_str = '<span class="s_pc_zfenx" data-id="http\://ent\.qianlong\.com/\d+/\d+/\d+\.shtml" data-title="(.*?)">'
# 	# for title in re.findall(re_str,browser.html):
# 	# 	print(title)

# 	html = etree.HTML(browser.html)

# 	# /html/body/div[1]/div[2]/div/div[1]/div/div
# 	# /html/body/div[1]/div[2]/div/div[1]/div/div

# 	# /html/body/div[1]/div[2]/div/div[1]/div[3]/div[1]/
# 	# /html/body/div[1]/div[2]/div/div[1]/div[3]/div[2]/div[1]/a/text()

# 	# /html/body/div[1]/div[2]/div/div[1]/div[3]/div[1]/
# 	# /html/body/div[1]/div[2]/div/div[1]/div[3]/div[2]/div[3]/div/span

# 	# for div in html.xpath('/html/body/div[1]/div[2]/div/div[1]/div[3]/div'):
# 	# 	title = div.xpath('div[1]/a/text()')[0]
# 	# 	date = div.xpath('div[3]/div/span/text()')[0]
# 	# 	print(title.replace('  ','').replace('\n',''),date)

# 	for a in html.xpath('/html/body/div[1]/div[2]/div/div[1]/div[3]/div/div[1]/a'):
# 		print(a.xpath('@href')[0],a.xpath('text()')[0])

# browser.quit()

# import pickle

# user = {'姓名':'小曾','性别':'男'}
# with open('user.pickle','wb') as file:
	# pickle.dump(user,file)

# with open('user.pickle','rb') as file:
# 	user = pickle.load(file)
# print(user)