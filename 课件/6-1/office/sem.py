import os,openpyxl

xlsx = openpyxl.load_workbook('sem\\周一.xlsx')
sheet = xlsx['Sheet1']

# for r in range(2,26):
# 	rl = []
# 	for c in [1,2,3,4]:
# 		rl.append(sheet.cell(r,c).value)
# 	print(rl)

# consume_list = []
# for r in range(2,26):
# 	consume_list.append(int(sheet.cell(r,2).value))
# consume = sum(consume_list)

for file_name in os.listdir('sem'):
	if '.xlsx' not in file_name:
		continue

	xlsx = openpyxl.load_workbook('sem\\%s' % file_name)
	sheet = xlsx['Sheet1']

	consume = sum([int(sheet.cell(r,2).value) for r in range(2,26)])
	show = sum([int(sheet.cell(r,3).value) for r in range(2,26)])
	click = sum([int(sheet.cell(r,4).value) for r in range(2,26)])

	print('%s\t%s\t%s\t%s' % (file_name.replace('.xlsx',''),consume,show,click))






new_xlsx = openpyxl.Workbook()
new_sheet = new_xlsx.create_sheet('Sheet1',index=0)

xlsx = openpyxl.load_workbook('sem\\周一.xlsx')
sheet = xlsx['Sheet1']
time_list = ['星期'] + [sheet.cell(r,1).value for r in range(2,26)]
new_sheet.append(time_list)

for file_name in os.listdir('sem'):
	if '.xlsx' not in file_name:
		continue

	xlsx = openpyxl.load_workbook('sem\\%s' % file_name)
	sheet = xlsx['Sheet1']

	new_sheet.append([file_name.replace('.xlsx','')] + [int(sheet.cell(r,2).value) for r in range(2,26)])

new_xlsx.save('test.xlsx')


