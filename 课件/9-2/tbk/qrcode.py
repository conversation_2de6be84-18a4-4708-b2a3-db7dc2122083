from PIL import Image,ImageDraw,ImageFont

oimg = Image.open('oimg.jpg')
test = Image.open('img\\test.jpg')
oimg.paste(test,box=(0,0))

draw = ImageDraw.Draw(oimg)
fontStyle = ImageFont.truetype("HuXiaoBoNanShenTi-2.otf",size=20,encoding="utf-8")

text = '大容量304不锈钢保温水杯男1500ml水壶便携个人专用泡茶杯子1000'
if len(text) > 20:text = text[0:20] + '\n' + text[20:]
draw.text((5,370),text,(0,0,0),font=fontStyle)

fontStyle = ImageFont.truetype("HuXiaoBoNanShenTi-2.otf",size=15,encoding="utf-8")
data = {'258':(65,458),'158':(160,458),'100':(65,500),'10000+':(160,500)}
for text,box in data.items():
	draw.text(box,text,(0,0,0),font=fontStyle)

oimg.save('result.jpg','jpeg')

