# pip3 install opencv-python
# pip3 install moviepy

import os,cv2
from moviepy.editor import *

# path = 'mp4' + os.sep + 'test.mp4'
# fourcc = cv2.VideoWriter_fourcc('m','p','4','v')
# fps = 24
# size = (828,1792)
# video = cv2.VideoWriter(path,fourcc,fps,size)

# for i in range(1,11):
# 	img = cv2.imread('img' + os.sep + '%s.png' % i)

# 	for f in range(0,fps):
# 		video.write(img)

# video.release()

# ------------------------------------------------------------------------------

video1 = VideoFileClip('mp4' + os.sep + '1.mp4')
video2 = VideoFileClip('mp4' + os.sep + '2.mp4')

video_list = [video1,video2]
video = concatenate_videoclips(video_list)

video.write_videofile('mp4' + os.sep + '3.mp4')