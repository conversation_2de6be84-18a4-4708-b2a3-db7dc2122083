# print('hello,world!')

# 1
# 2

# 12

# 数值

# 1.1
# 1.34

# 'bsh你是11'
# "sasa"

# True False

# None


# ['a','b']
# [1,'a',True,[1,2,3]]

# (1,2,3)

# set()

# set([1,2])

# [1,1,2]

# set([1,1,2]) set([1,2])

# set(['url1','url2'])

# url3

# ['url1','url2']

# 哈希
# 无视元素数量，

# {}

# {'姓名':'小曾','性别':'男'}

# 键值对 视为一个元素

# json

# if 1 == 2:
# 	print('1')
# else:
# 	print('2')

# a = 1+1

# print(a)
# print(a+1)

# p = 3.14159

# a = []

# a

# for i in [1,2,3,4,5]:
# 	print(i)
# for j in ['a','b','c']:
# 	print(1)

i = 0
while i < 10:
	print(1)
	i = i + 1
print(2)

