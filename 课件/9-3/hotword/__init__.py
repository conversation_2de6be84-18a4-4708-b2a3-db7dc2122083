import os,re,json
from lxml import etree
from splinter.browser import Browser

browser = Browser(driver_name='chrome')

api_url = {
	'tieba':'http://tieba.baidu.com/hottopic/browse/topicList',
	'toutiao':'https://www.toutiao.com/hot-event/hot-board/?origin=toutiao_pc',
	'baidu':'https://top.baidu.com/board?tab=realtime',
	'bilibili':'https://www.bilibili.com/v/popular/rank/all',
	'weibo':'https://weibo.com/ajax/side/hotSearch',
	'zhihu':'https://www.zhihu.com/api/v3/feed/topstory/hot-list-web?limit=50&desktop=true',
	'kuaishou':'https://www.kuaishou.com/',
	'douyin':'https://www.iesdouyin.com/aweme/v1/web/hot/search/list/',
	'shenma':'https://m.sm.cn/s?q=%E7%A5%9E%E9%A9%AC%E6%96%B0%E9%97%BB%E6%A6%9C%E5%8D%95'
}