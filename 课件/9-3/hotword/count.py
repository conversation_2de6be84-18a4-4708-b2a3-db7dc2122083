from __init__ import *

def get_data(url):
	try:
		browser.visit(url)
	except Exception as e:
		return False,str(e)
	return True,browser.html
def get_tieba(data):
	rel_obj = json.loads(re.findall('<pre style="word-wrap: break-word; white-space: pre-wrap;">(.*?)</pre>',data,re.S)[0])
	new_hot = [(data['topic_name'],data['discuss_num']) for data in rel_obj['data']['bang_topic']['topic_list']]
	return new_hot
def get_toutiao(data):
	rel_obj = json.loads(re.findall('<pre style="word-wrap: break-word; white-space: pre-wrap;">(.*?)</pre>',data,re.S)[0])
	new_hot = [(data['Title'],data['HotValue']) for data in rel_obj['data']]
	return new_hot
def get_weibo(data):
	rel_obj = json.loads(re.findall('<pre style="word-wrap: break-word; white-space: pre-wrap;">(.*?)</pre>',data,re.S)[0])
	new_hot = [(data['word'],data['raw_hot']) for data in rel_obj['data']['realtime'] if 'raw_hot' in data]
	return new_hot
def get_zhihu(data):
	rel_obj = json.loads(re.findall('<pre style="word-wrap: break-word; white-space: pre-wrap;">(.*?)</pre>',data,re.S)[0])
	new_hot = [(data['target']['title_area']['text'],data['target']['metrics_area']['text'].replace(' 万热度','') + '0000') for data in rel_obj['data']]
	return new_hot
def get_douyin(data):
	rel_obj = json.loads(re.findall('<pre style="word-wrap: break-word; white-space: pre-wrap;">(.*?)</pre>',data,re.S)[0])
	new_hot = [(data['word'],data['hot_value']) for data in rel_obj['data']['word_list']]
	return new_hot

def get_baidu(data):
	html = etree.HTML(data)
	new_hot = []
	for div in html.xpath('//*[@id="sanRoot"]/main/div[2]/div/div[2]/div'):
		word = div.xpath('div[2]/a/div[1]/text()')[0]
		hot = div.xpath('div[1]/div[2]/text()')[0]
		new_hot.append((word.replace(' ',''),hot.replace(' ','')))
	return new_hot
def get_bilibili(data):
	html = etree.HTML(data)
	new_hot = []
	for div in html.xpath('//*[@id="app"]/div/div[2]/div[2]/ul/li'):
		word = div.xpath('div/div[2]/a/text()')[0]
		hot = div.xpath('div/div[2]/div/div/span[1]/text()')[0]
		hot = str(int(float(hot.replace(' ','').replace('\n','').replace('万','')) * 10000))
		new_hot.append((word,hot))
	return new_hot
def get_shenma(data):
	html = etree.HTML(data)
	new_hot = []
	for div in html.xpath('//*[@id="sc_news_top_list_lg_1_1"]/div[2]/a'):
		word = div.xpath('div/span[2]/span/text()')[0]
		hot = div.xpath('div/span[3]/text()')[0]
		hot = str(int(float(hot.replace('万关注','')) * 10000))
		new_hot.append((word,hot))
	return new_hot
def get_kuaishou(data):
	new_hot = []
	for i in range(0,5):
		html = etree.HTML(data)
		for div in html.xpath('//*[@id="app"]/div[1]/section/div/div/div[3]/div[1]/div[2]/div[1]/div'):
			word = div.xpath('div/p/text()')[0]
			hot = div.xpath('div/div/span[2]/text()')[0]
			hot = str(int(float(hot.replace('w热度','')) * 10000))
			new_hot.append((word.replace(' ',''),hot))
		browser.find_by_xpath('//*[@id="app"]/div[1]/section/div/div/div[3]/div[1]/div[2]/div[2]/button[2]')[0].click()
	return new_hot

if __name__ == '__main__':
	pass

	status,data = get_data(api_url['tieba'])
	if status == True:
		new_hot = get_kuaishou(data)
		print(new_hot)
	else:
		print(data)


	
	
	