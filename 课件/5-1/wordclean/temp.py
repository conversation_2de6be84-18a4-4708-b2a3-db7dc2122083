import re

# s = '自媒体发展1223'
# l = 'sddsf'
# print(re.findall('\d',l))

# print(re.findall('1(\d+)3',s))

# print(re.findall('\d','自媒体自哪1些23'))
# print(re.findall('[0-9]+','自媒体自哪1些23'))
# print(re.findall('[a-zA-Z]+','自seo媒A体afgifn自哪1f些23w'))
# print(re.findall('\d+','自媒体自哪1些211111111111111111113'))

# if len(re.findall('\d',keyword)) > 0:
# 	continue

html = '''
<html>
	<body>
		<div>
			<table>
				<tr>
					<td>问题</td>
					<td>答案</td>
					<td>浏览</td>
					<td>点赞</td>
					<td>评论</td>
				</tr>
				<tr>
					<td style="">问题</td>
					<td>答案</td>
					<td>浏览</td>
					<td>点赞</td>
					<td>评论</td>
					<td><span>评论</span></td>
				</tr>
			</table>
		</div>
	</body>
</html>
'''
print(re.findall('<td>(.*?)</td>',html))

s = '123456'
print(re.sub('|','a',s))

s = 'sasda1234567@qq.com12212'
print(re.findall('asdasdassdfbsdhfsdhdhdhbfsdhfshdfhdsfhdsfhsdfjhajbsdhasbdfbshfb',s))

'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'

# http://erji.sians.aa-111.cn/sas/sasdf/dgfdsfg/dfgdfg/1112.shtml

href="(.*?)"