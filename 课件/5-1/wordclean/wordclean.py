import re

with open('data.txt','r',encoding='utf-8') as file:
	keyword_list = file.read().split('\n')

for keyword in keyword_list:
	if len(keyword) < 5 or len(keyword) > 8:
		continue

	# if '赚' in keyword or '挣' in keyword:
	# 	continue
	# is_true = False
	# for i in ['赚','挣']:
	# 	if i in keyword:
	# 		is_true = True
	# 		break
	# if is_true == True:
	# 	continue
	if len(re.findall('|'.join(['赚','挣']),keyword)) > 0:
		continue

	if '伪原创' in keyword and '文章' in keyword:
		continue
	if keyword.isdigit() == True:
		continue

	# is_true = False
	# for i in ['0','1','2','3','4','5','6','7','8','9']:
	# 	if i in keyword:
	# 		is_true = True
	# 		break
	# if is_true == True:
	# 	continue
	if len(re.findall('\d',keyword)) > 0:
		continue

	# if ' ' in keyword:
	# 	keyword = keyword.replace(' ','')
	keyword = re.sub(' ','',keyword)
	print(keyword)

