from collections import defaultdict
import jieba.posseg as jp

with open('keyword.txt','r',encoding='utf-8') as file:
	keyword_list = file.read().split('\n')

not_flag = set(['w','x','y','z','un','m'])
not_word = set(['的','是','有','啊','呢','么','好'])

keyword_split = dict()
word_count = defaultdict(int)
for keyword in keyword_list:
	word_set = set()
	for word,flag in jp.cut(keyword):
		if flag in not_flag:
			continue
		if word in not_word:
			continue
		if word == 'pdf' or word == 'PDF':
			continue

		word_count[word] += 1
		word_set.add(word)

	keyword_split[keyword] = word_set

id_keyword_list = defaultdict(list)
id_count = defaultdict(int)
for keyword,word_set in keyword_split.items():
	word_sort = dict()
	for word in word_set:
		word_sort[word] = word_count[word]

	word_sort = sorted(word_sort.items(),key=lambda x:x[1],reverse=True)
	word_id = ','.join([word for word,count in word_sort[0:3]])

	id_keyword_list[word_id] += [keyword]
	id_count[word_id] += 1

result = []
id_count = sorted(id_count.items(),key=lambda x:x[1],reverse=True)
for word_id,count in id_count:
	if count < 3:
		continue
	for keyword in id_keyword_list[word_id]:
		result.append('%s\t%s' % (keyword,word_id))
	result.append('')

with open('result.txt','wb') as file:
	file.write('\n'.join(result).encode('utf-8'))