import jieba,jieba.posseg as jp

jieba.load_userdict('dict.txt')
jieba.del_word('媒体广告')

# keyword_list = ['seo如何优化','seo如何提升排名']
with open('keyword.txt','r',encoding='utf-8') as file:
	keyword_list = file.read().split('\n')

word_count = dict()
for keyword in keyword_list:
	for word,flag in jp.cut(keyword):
		if word in word_count:
			word_count[word] = word_count[word] + 1
		else:
			word_count[word] = 1

for word,count in word_count.items():
	print('%s\t%s' % (word,count))