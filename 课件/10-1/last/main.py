# with open('1.txt','r',encoding='gbk') as file:
# 	print(file.read())

# with open('2.txt','wb') as file:
# 	file.write('阿达'.encode('utf-8'))

# from splinter.browser import Browser
# import requests

# browser = Browser(driver_name='chrome')
# browser.visit('http://www.shcaoan.com/wy/satellite/')

# rel = requests.get('http://www.shcaoan.com/wy/satellite/')
# rel.encoding = 'gbk'

# print(rel.text)

# def get_keyword(word):
# 	return keyword_list

# from queue import Queue

# _q = Queue()

# main_key = 'seo'
# _q.put(main_key)
# set()

# while not _q.empty():
# 	word = _q.get()
# 	keyword_list = get_keyword(word)
# 	for keyword in keyword_list:
# 		for _word,flag in jp.cut(keyword):
# 			if _word in set():
# 				continue
# 			if main_key != _word:
# 				_q.put(main_key +' '+ _word)
# 				set().add(_word)


class count_ab():
	def __init__(self):
		self.c = 3
	def ab1(self,a,b):
		return a+b+self.c
	def ab2(self,a,b):
		return a*b
	def ab3(self):
		pass

ab = count_ab()

print(ab.ab1(1,2))
print(ab.c)

