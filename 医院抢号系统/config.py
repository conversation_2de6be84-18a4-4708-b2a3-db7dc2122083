#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院挂号系统配置文件
"""

# API配置
API_CONFIG = {
    "base_url": "https://tjhapp.com.cn:8013",
    "endpoint": "/yuyue/getdocinfoNewV2",
    "timeout": 30
}

# 请求头配置（从HAR文件中提取）
HEADERS = {
    "Host": "tjhapp.com.cn:8013",
    "uuid": "oqrz4jiS2A2OMaAbk5p7RX9fHd9M",
    "Accept": "application/json",
    "X-Requested-With": "XMLHttpRequest",
    "ukey": "0145409357c309ff171da9d53b949432",
    "uname": "13971563836",
    "Accept-Language": "zh-CN,zh-Hans;q=0.9",
    "token": "eJwB0AAv_2-_7yxsSC2zKv9ZTAe7L_QDj25_afNX4bqwDWEknhvFfOW8b3KTX0auG4hr4mDslFMtaZL-aAbKtTTCctdLSsBa32rH2Nz15UcqSUuqiiVZv0E4arCO06qFYcJ1rwtR36RrcrzZXbI7lucFS45290qtaTj2T4QEeoFfW0luhmE9w6w479XcBgxA5wd1Y-ZJtuoxvmAMcMnGhHm_lTXKeoXvhSid6o4HaJ28Szt5B8vV4w_kltITXch7lPqP14GifpPWC7YXVmVCrks3yTtKZ14D91FokTI=",
    "Accept-Encoding": "gzip, deflate, br",
    "Origin": "https://tjhapp.com.cn",
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.60(0x18003c32) NetType/WIFI Language/zh_CN",
    "Referer": "https://tjhapp.com.cn/",
    "plan": "wxapp",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded"
}

#监控配置
MONITOR_CONFIG = {
    "doctor_code": "100403",    # 要监控的医生代码
    "doctor_name": "罗小平",     # 医生姓名（用于显示）
    "max_fee": 50.0,           # 最大挂号费（元）
    "monitor_interval": 1,      # 监控间隔（分钟）
}
# MONITOR_CONFIG = {
#     "doctor_code": "102304",    # 要监控的医生代码
#     "doctor_name": "姜玉萍",     # 医生姓名（用于显示）
#     "max_fee": 50.0,           # 最大挂号费（元）
#     "monitor_interval": 1,      # 监控间隔（分钟）
# }


# 默认请求参数
DEFAULT_PARAMS = {
    "yq_code1": "",          # 院区代码1
    "ks_code1": "",          # 科室代码1
    "schedule_type": "",     # 排班类型
    "later_than_17": True    # 是否17点后
}

# 常用医生代码（可以根据需要添加更多）
DOCTOR_CODES = {
    "罗小平": "100403",
    # 可以添加更多医生的代码
    # "其他医生": "医生代码",
}

# 输出配置
OUTPUT_CONFIG = {
    "save_raw_data": True,
    "raw_data_file": "医院抢号系统/doctor_info_raw.json",
    "log_level": "INFO"
}
